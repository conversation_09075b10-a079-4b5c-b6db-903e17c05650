import React from 'react';
import Mark<PERSON><PERSON>enderer from '../common/chips/Markdown';

const ExactBackendTest: React.FC = () => {
  // This should match exactly what your backend is sending
  const backendResponse = `"summary": "Thank you for uploading your document. What would you like to do next ?",

Table of Greek Symbols
Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |
| Delta | Delta | \\delta |
| Epsilon | Epsilon | \\epsilon |
| Lambda | Lambda | \\lambda |
| Mu | Mu | \\mu |
| Sigma | Sigma | \\sigma |
| Pi | Pi | \\pi |
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |`;

  // Test with individual problematic cases
  const thetaTest = "| Symbol | Name | LaTeX Code |\n|--------|------|------------|\n| Theta | Theta | \\theta |";
  const omegaTest = "| Symbol | Name | LaTeX Code |\n|--------|------|------------|\n| Omega | Omega | \\omega |";

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1000px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Exact Backend Response Test</h1>
      
      {/* Full Backend Response */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Full Backend Response:</h3>
        <MarkdownRenderer content={backendResponse} />
      </div>

      {/* Theta Test */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Theta Test (should show θ in Symbol and Name columns):</h3>
        <MarkdownRenderer content={thetaTest} />
        
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
          <strong>Raw input:</strong> <code>{thetaTest}</code>
        </div>
      </div>

      {/* Omega Test */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Omega Test (should show ω in Symbol and Name columns):</h3>
        <MarkdownRenderer content={omegaTest} />
        
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
          <strong>Raw input:</strong> <code>{omegaTest}</code>
        </div>
      </div>

      {/* Character by character test */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <h3>Character Analysis:</h3>
        <div style={{ fontFamily: 'monospace', fontSize: '14px' }}>
          <div><strong>Word "Theta":</strong></div>
          <div>T: {JSON.stringify("T")} (char code: {"T".charCodeAt(0)})</div>
          <div>h: {JSON.stringify("h")} (char code: {"h".charCodeAt(0)})</div>
          <div>e: {JSON.stringify("e")} (char code: {"e".charCodeAt(0)})</div>
          <div>t: {JSON.stringify("t")} (char code: {"t".charCodeAt(0)})</div>
          <div>a: {JSON.stringify("a")} (char code: {"a".charCodeAt(0)})</div>
          
          <div style={{ marginTop: '10px' }}><strong>Full word:</strong> {JSON.stringify("Theta")}</div>
          
          <div style={{ marginTop: '10px' }}><strong>Backend response contains:</strong></div>
          <div>- "Theta": {backendResponse.includes("Theta") ? "✅ Yes" : "❌ No"}</div>
          <div>- "heta": {backendResponse.includes("heta") ? "⚠️ Yes (corrupted)" : "✅ No"}</div>
        </div>
      </div>
    </div>
  );
};

export default ExactBackendTest;
