import {
  BrowserRouter as Router,
  Routes,
  Route,
  useNavigate,
} from 'react-router-dom';
import { Provider, useDispatch } from 'react-redux';
import { store } from './store/store';
import './App.css';
import { useEffect, lazy, Suspense } from 'react';
import { setAuth } from './store/authSlice';
import useLocalStorage from './hooks/useLocalStorage';
import { useNotifications } from './hooks/useNotifications';
import { DialogProvider } from './contexts/DialogContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { TourProvider } from './contexts/TourContext';
import { allTours } from './utils/tourDefinitions';
import useNotificationInit from './hooks/useNotificationInit';
import './utils/testNotificationAPI'; // Load test utility

// Layouts
import Layout from './components/layout/Layout';
import NoLayout from './components/layout/NoLayout';
import PrivateRoute from './components/layout/PrivateRoute';

// Common components
import AuthExpiredDialog from './components/common/AuthExpiredDialog';
import LoadingSpinner from './components/common/loading/LoadingSpinner';
import Library from './pages/Library/Library';

// Lazy loaded components
const LoginPage = lazy(() => import('./pages/loginpage/LoginPage'));
const RegistrationPage = lazy(
  () => import('./pages/registrationpage/RegistrationPage')
);
const HomePage = lazy(() => import('./pages/homepage/HomePage'));
const ChatPage = lazy(() => import('./pages/chatpage/ChatPage'));
const MyProjectsPage = lazy(
  () => import('./pages/myprojectspage/MyProjectsPage')
);
const AwaitingActionsPage = lazy(
  () => import('./pages/awaitedactions/AwaitingActionsPage')
);
const AwaitedActionsViewPage = lazy(
  () => import('./pages/awaitedactions/AwaitedActionsViewPage')
);
const SketchBookPage = lazy(
  () => import('./pages/sketchbookpage/SketchBookPage')
);
const SketchbooksHome = lazy(
  () => import('./pages/sketchbookpage/SketchbooksHome')
);
const Workflow = lazy(() => import('./pages/workflow/WorkFlow'));
const HomeFinal = lazy(() => import('./pages/homeFinal/HomeFinal'));
const AdminPage = lazy(() => import('./pages/admin/AdminPage'));
const PageNotFound = lazy(() => import('./components/layout/PageNotFound'));

// Test components
const KaTeXPluginTest = lazy(() => import('./components/test/KaTeXPluginTest'));
const SimpleGreekTest = lazy(() => import('./components/test/SimpleGreekTest'));
const DebugStepByStep = lazy(() => import('./components/test/DebugStepByStep'));
const ExactBackendTest = lazy(
  () => import('./components/test/ExactBackendTest')
);
const DebugTableCells = lazy(() => import('./components/test/DebugTableCells'));

// Specific components
const PriceScreen = lazy(
  () => import('./components/specific/priceScreen/PriceScreen')
);
const EditProfile = lazy(
  () => import('./components/specific/editProfile/EditProfile')
);

// Payment components
const PaymentPage = lazy(() => import('./pages/payment/PaymentPage'));
const PaymentSuccessPage = lazy(
  () => import('./pages/payment/PaymentSuccessPage')
);
const PaymentCancelPage = lazy(
  () => import('./pages/payment/PaymentCancelPage')
);

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <Router>
          <DialogProvider>
            <TourProvider tours={allTours as any}>
              <AppContent />
            </TourProvider>
          </DialogProvider>
        </Router>
      </ThemeProvider>
    </Provider>
  );
}

function AppContent() {
  const dispatch = useDispatch();
  const [user] = useLocalStorage('user', null);
  const isAuthenticated = !!localStorage.getItem('token');

  // Initialize notifications with API enabled only once at app level
  const { refreshNotifications } = useNotifications(true);

  // Initialize notifications (now simplified)
  useNotificationInit();

  // Fetch notifications when user logs in
  useEffect(() => {
    if (isAuthenticated && user && refreshNotifications) {
      refreshNotifications();
    }
  }, [isAuthenticated, user, refreshNotifications]);

  useEffect(() => {
    const handleStorageChange = () => {
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
      const storedToken = localStorage.getItem('token');

      console.log(storedUser, 'hello user');
      if (storedUser && storedToken) {
        dispatch(setAuth({ token: storedToken, userDetails: storedUser }));
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Call the function initially to set the auth state when the component mounts
    handleStorageChange();

    return () => window.removeEventListener('storage', handleStorageChange);
  }, [dispatch]);

  const navigate = useNavigate();

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated]);

  return (
    <>
      <AuthExpiredDialog />
      {/* Loading spinner for lazy-loaded components */}
      <Suspense
        fallback={
          <LoadingSpinner fullScreen size="large" message="Loading page..." />
        }
      >
        <Routes>
          {isAuthenticated && (
            <Route element={<Layout />}>
              <Route
                path="/"
                element={
                  <PrivateRoute>
                    <HomePage />
                  </PrivateRoute>
                }
              />
              {user?.role === 'ROLE_ADMIN' && (
                <Route
                  path="/dashboard"
                  element={
                    <PrivateRoute>
                      <AdminPage />
                    </PrivateRoute>
                  }
                />
              )}
              <Route
                path="/chat"
                element={
                  <PrivateRoute>
                    <ChatPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/my-projects"
                element={
                  <PrivateRoute>
                    <MyProjectsPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/library"
                element={
                  <PrivateRoute>
                    <Library />
                  </PrivateRoute>
                }
              />
              <Route
                path="/awaiting-actions"
                element={
                  <PrivateRoute>
                    <AwaitingActionsPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/awaited-actions-view"
                element={
                  <PrivateRoute>
                    <AwaitedActionsViewPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/sketchbooklists"
                element={
                  <PrivateRoute>
                    <SketchbooksHome />
                  </PrivateRoute>
                }
              />

              <Route
                path="/workflow"
                element={
                  <PrivateRoute>
                    <Workflow />
                  </PrivateRoute>
                }
              />

              <Route
                path="/home-final"
                element={
                  <PrivateRoute>
                    <HomeFinal />
                  </PrivateRoute>
                }
              />
              <Route
                path="/edit-profile"
                element={
                  <PrivateRoute>
                    <EditProfile />
                  </PrivateRoute>
                }
              />
              <Route
                path="/test-katex"
                element={
                  <PrivateRoute>
                    <KaTeXPluginTest />
                  </PrivateRoute>
                }
              />
              <Route
                path="/test-greek"
                element={
                  <PrivateRoute>
                    <SimpleGreekTest />
                  </PrivateRoute>
                }
              />
              <Route
                path="/debug-greek"
                element={
                  <PrivateRoute>
                    <DebugStepByStep />
                  </PrivateRoute>
                }
              />
              <Route
                path="/test-backend"
                element={
                  <PrivateRoute>
                    <ExactBackendTest />
                  </PrivateRoute>
                }
              />
              <Route
                path="/debug-cells"
                element={
                  <PrivateRoute>
                    <DebugTableCells />
                  </PrivateRoute>
                }
              />
              <Route
                path="*"
                element={
                  <PrivateRoute>
                    <PageNotFound />
                  </PrivateRoute>
                }
              />
            </Route>
          )}

          <Route element={<NoLayout />}>
            <Route path="/sketchbook" element={<SketchBookPage />} />
            <Route path="/sketchbook/:id" element={<SketchBookPage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<RegistrationPage />} />
            <Route path="/price" element={<PriceScreen />} />
            <Route path="/payment" element={<PaymentPage />} />
            <Route path="/payment/success" element={<PaymentSuccessPage />} />
            <Route path="/payment/error" element={<PaymentCancelPage />} />
          </Route>
        </Routes>
      </Suspense>
    </>
  );
}

export default App;
