import React from 'react';
import { convertGreekLetters } from '../../utils/markdownUtils';

const DebugTableCells: React.FC = () => {
  // Test the exact table format
  const testTable = `| Symbol | Name | LaTeX Code |
|--------|------|------------|
| ( $\\alpha$ ) | Alpha | \\alpha |
| ( $\\beta$ ) | Beta | \\beta |
| ( $\\gamma$ ) | Gamma | \\gamma |`;

  // Test individual cell processing
  const testLine = "| ( $\\alpha$ ) | Alpha | \\alpha |";
  const cells = testLine.split('|');
  
  console.log('Debug: Table cells breakdown:');
  cells.forEach((cell, index) => {
    console.log(`Cell ${index}: "${cell}" (trimmed: "${cell.trim()}")`);
  });

  const processedTable = convertGreekLetters(testTable);

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1000px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Debug Table Cell Processing</h1>
      
      {/* Original Table */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Original Table:</h3>
        <pre style={{ 
          fontSize: '12px', 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px'
        }}>
          {testTable}
        </pre>
      </div>

      {/* Processed Table */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>Processed Table:</h3>
        <pre style={{ 
          fontSize: '12px', 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px'
        }}>
          {processedTable}
        </pre>
      </div>

      {/* Cell Analysis */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <h3>Cell Analysis for: {testLine}</h3>
        <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {cells.map((cell, index) => (
            <div key={index} style={{ marginBottom: '5px' }}>
              <strong>Cell {index}:</strong> "{cell}" 
              <br />
              <span style={{ marginLeft: '20px', color: '#666' }}>
                Trimmed: "{cell.trim()}" | Length: {cell.trim().length}
              </span>
              <br />
              <span style={{ marginLeft: '20px', color: '#666' }}>
                Contains backslash: {cell.includes('\\') ? 'Yes' : 'No'} | 
                Contains $: {cell.includes('$') ? 'Yes' : 'No'}
              </span>
            </div>
          ))}
        </div>

        <div style={{ marginTop: '20px' }}>
          <h4>Pattern Tests:</h4>
          <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
            {cells.map((cell, index) => {
              const trimmed = cell.trim();
              const pattern1 = /^\(\\([a-zA-Z]+)\)$/.test(trimmed);
              const pattern2 = /^\(\$\\([a-zA-Z]+)\$\)$/.test(trimmed);
              
              return (
                <div key={index} style={{ marginBottom: '5px' }}>
                  <strong>Cell {index}:</strong> "{trimmed}"
                  <br />
                  <span style={{ marginLeft: '20px', color: '#666' }}>
                    Pattern 1 (\\alpha): {pattern1 ? '✅ Match' : '❌ No match'}
                  </span>
                  <br />
                  <span style={{ marginLeft: '20px', color: '#666' }}>
                    Pattern 2 ($\\alpha$): {pattern2 ? '✅ Match' : '❌ No match'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugTableCells;
