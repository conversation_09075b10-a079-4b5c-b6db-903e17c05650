// React import removed as it's not needed in modern React
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeKatex from 'rehype-katex';
import remarkMath from 'remark-math';
// import rehypeRaw from 'rehype-raw'; // Commented out until installed
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import './markdownStyles.css';
import { AI_BASE_URL } from '../../../services/config';
import {
  preprocessMarkdownContent,
  detectLanguage,
} from '../../../utils/markdownUtils';

// TypeScript interfaces
interface MarkdownRendererProps {
  content: string;
  baseUrl?: string;
  theme?: 'light' | 'dark';
  enableInlineHtml?: boolean;
  enableGreekConversion?: boolean;
  enableCurrencyProtection?: boolean;
  enableLatexPreprocessing?: boolean;
  className?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  baseUrl = AI_BASE_URL,
  theme = 'light', // eslint-disable-line @typescript-eslint/no-unused-vars
  enableInlineHtml = false, // eslint-disable-line @typescript-eslint/no-unused-vars
  enableGreekConversion = true,
  enableCurrencyProtection = true,
  enableLatexPreprocessing = true,
  className = '',
}) => {
  // Note: theme and enableInlineHtml are kept for backward compatibility
  // and future enhancements
  // Ensure content is a string and handle edge cases
  if (!content || typeof content !== 'string') {
    return <div className={`markdown-wrapper ${className}`}></div>;
  }

  try {
    // Use the comprehensive preprocessing pipeline
    const processedContent = preprocessMarkdownContent(content, {
      baseUrl,
      enableGreekConversion: false, // Temporarily disabled for testing
      enableCurrencyProtection: false, // Temporarily disabled for testing
      enableLatexPreprocessing: false, // Temporarily disabled for testing
    });


    // Configure plugins based on options
    const remarkPlugins: any[] = [
      remarkGfm,
      [remarkMath, { singleDollarTextMath: true }],
    ];

    const rehypePlugins: any[] = [rehypeKatex];
    // Add rehypeRaw for HTML support when available and enabled
    // if (enableInlineHtml && rehypeRaw) {
    //   rehypePlugins.push(rehypeRaw);
    // }

    return (
      <div className={`markdown-wrapper ${className}`}>
        <ReactMarkdown
          className="markdown"
          remarkPlugins={remarkPlugins}
          rehypePlugins={rehypePlugins}
          components={{
            // Enhanced code block rendering with language detection
            code({ className, children, ...props }: any) {
              const codeString = String(children).replace(/\n$/, '');
              const match = /language-(\w+)/.exec(className || '');
              const language = match ? match[1] : detectLanguage(codeString);
              const isInline = !className || !match;

              return !isInline ? (
                <SyntaxHighlighter
                  style={solarizedlight as any}
                  language={language}
                  PreTag="div"
                  showLineNumbers={codeString.split('\n').length > 5}
                  wrapLines={true}
                >
                  {codeString}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
            // Enhanced table rendering
            table({ children, ...props }) {
              return (
                <div className="table-wrapper">
                  <table className="markdown-table" {...props}>
                    {children}
                  </table>
                </div>
              );
            },
            // Enhanced checkbox rendering
            input({ type, checked, ...props }) {
              if (type === 'checkbox') {
                return (
                  <input
                    type="checkbox"
                    checked={checked}
                    readOnly
                    className="markdown-checkbox"
                    {...props}
                  />
                );
              }
              return <input type={type} {...props} />;
            },
            // Enhanced link rendering with external link handling
            a({ href, children, ...props }) {
              const isExternal =
                href?.startsWith('http') || href?.startsWith('//');
              return (
                <a
                  href={href}
                  target={isExternal ? '_blank' : undefined}
                  rel={isExternal ? 'noopener noreferrer' : undefined}
                  className="markdown-link"
                  {...props}
                >
                  {children}
                </a>
              );
            },
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  } catch (error) {
    console.error('MarkdownRenderer Error:', error);
    console.error('Content that caused error:', content);

    // Simple fallback rendering
    return (
      <div className={`markdown-wrapper ${className}`}>
        <div
          style={{
            color: '#d32f2f',
            fontSize: '12px',
            marginBottom: '8px',
            padding: '8px',
            backgroundColor: '#ffebee',
            border: '1px solid #ffcdd2',
            borderRadius: '4px',
          }}
        >
          ⚠️ Rendering error - displaying as plain text
        </div>
        <div
          style={{
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            padding: '8px',
            backgroundColor: '#f5f5f5',
            border: '1px solid #ddd',
            borderRadius: '4px',
          }}
        >
          {content}
        </div>
      </div>
    );
  }
};

// Named export for more explicit usage
export { MarkdownRenderer as EnhancedMarkdownRenderer };

// Export with backward compatibility
export default MarkdownRenderer;
