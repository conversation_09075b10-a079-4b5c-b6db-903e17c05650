import React from 'react';
import <PERSON><PERSON><PERSON>enderer from '../common/chips/Markdown';

const GreekSymbolTest: React.FC = () => {
  const testContent = `
# Greek Symbol Rendering Test

## Test Case 1: Backend Response Format
Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |
| Delta | Delta | \\delta |
| Epsilon | Epsilon | \\epsilon |
| Lambda | Lambda | \\lambda |
| Mu | Mu | \\mu |
| Sigma | Sigma | \\sigma |
| Pi | Pi | \\pi |
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |

## Test Case 2: Direct Greek Letter Names
The following should auto-convert to Greek symbols:
- Alpha and Beta are the first two letters
- Gamma radiation is dangerous
- Delta represents change
- Epsilon is very small
- Lambda functions are useful
- Mu is the mean
- Sigma represents standard deviation
- Pi equals approximately 3.14159
- Theta is an angle
- Phi is the golden ratio
- Omega is the last letter

## Test Case 3: Mixed Content
In physics, the equation E = mc² uses various symbols. The Greek letter Alpha represents the fine structure constant, while Beta decay involves Beta particles. The angle Theta is measured in radians, and Pi appears in circular calculations.

## Test Case 4: Currency Protection Test
This should not render as math: The price is $100 and the cost is $200. However, this should render as math: $\\alpha + \\beta = \\gamma$.

## Test Case 5: Direct LaTeX
Direct LaTeX symbols: $\\alpha$, $\\beta$, $\\gamma$, $\\delta$, $\\epsilon$, $\\lambda$, $\\mu$, $\\sigma$, $\\pi$, $\\theta$, $\\phi$, $\\omega$
`;

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '800px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Greek Symbol Rendering Test</h1>
      
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#666', marginBottom: '15px' }}>Expected Results:</h3>
        <ul style={{ color: '#666', fontSize: '14px' }}>
          <li><strong>Test Case 1:</strong> Table should display properly with LaTeX codes as text</li>
          <li><strong>Test Case 2:</strong> Greek letter names should auto-convert to actual symbols (α, β, γ, etc.)</li>
          <li><strong>Test Case 3:</strong> Mixed content should show symbols where appropriate</li>
          <li><strong>Test Case 4:</strong> Currency ($100, $200) should remain as text, LaTeX should render as symbols</li>
          <li><strong>Test Case 5:</strong> Direct LaTeX should render as mathematical symbols</li>
        </ul>
      </div>

      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <MarkdownRenderer content={testContent} />
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '16px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '8px',
        border: '1px solid #ffeaa7'
      }}>
        <h3 style={{ color: '#856404' }}>Troubleshooting:</h3>
        <p style={{ color: '#856404', fontSize: '14px' }}>
          If Greek symbols are not rendering properly:
        </p>
        <ol style={{ color: '#856404', fontSize: '14px' }}>
          <li>Check browser console for KaTeX errors</li>
          <li>Verify that enableGreekConversion is set to true</li>
          <li>Ensure KaTeX CSS is loaded properly</li>
          <li>Check if remarkMath and rehypeKatex plugins are working</li>
        </ol>
      </div>
    </div>
  );
};

export default GreekSymbolTest;
