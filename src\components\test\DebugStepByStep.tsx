import React from 'react';
import { preprocessLatex, convertGreekLetters, protectCurrency, preprocessMarkdownContent } from '../../utils/markdownUtils';

const DebugStepByStep: React.FC = () => {
  // Test the exact content from your backend
  const originalContent = `| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |`;

  // Step by step processing
  const step1_original = originalContent;
  const step2_latexPreprocessed = preprocessLatex(originalContent);
  const step3_greekConverted = convertGreekLetters(step2_latexPreprocessed);
  const step4_currencyProtected = protectCurrency(step3_greekConverted);
  const step5_fullProcessed = preprocessMarkdownContent(originalContent, {
    enableGreekConversion: true,
    enableCurrencyProtection: true,
    enableLatexPreprocessing: true
  });

  const renderStep = (title: string, content: string, bgColor: string) => (
    <div style={{ 
      backgroundColor: 'white', 
      padding: '15px', 
      borderRadius: '8px',
      border: '1px solid #ddd',
      marginBottom: '15px'
    }}>
      <h4 style={{ color: '#666', marginBottom: '10px', backgroundColor: bgColor, padding: '5px', borderRadius: '4px' }}>
        {title}
      </h4>
      <pre style={{ 
        fontSize: '12px', 
        backgroundColor: '#f5f5f5', 
        padding: '10px', 
        borderRadius: '4px',
        overflow: 'auto',
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-all'
      }}>
        {content}
      </pre>
    </div>
  );

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Debug Step-by-Step Processing</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>Processing Pipeline Analysis</h2>
        <p style={{ color: '#666', fontSize: '14px' }}>
          This shows exactly what happens to your content at each step of the preprocessing pipeline.
        </p>
      </div>

      {renderStep('Step 1: Original Content', step1_original, '#e3f2fd')}
      
      {renderStep('Step 2: After LaTeX Preprocessing', step2_latexPreprocessed, '#f3e5f5')}
      
      {renderStep('Step 3: After Greek Letter Conversion', step3_greekConverted, '#e8f5e8')}
      
      {renderStep('Step 4: After Currency Protection', step4_currencyProtected, '#fff3e0')}
      
      {renderStep('Step 5: Full Pipeline Result', step5_fullProcessed, '#ffebee')}

      {/* Analysis */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <h3>Analysis:</h3>
        <div style={{ fontSize: '14px', color: '#666' }}>
          <h4>Changes Detected:</h4>
          <ul>
            <li><strong>LaTeX Preprocessing:</strong> {step2_latexPreprocessed !== step1_original ? '✅ Made changes' : '❌ No changes'}</li>
            <li><strong>Greek Conversion:</strong> {step3_greekConverted !== step2_latexPreprocessed ? '✅ Made changes' : '❌ No changes'}</li>
            <li><strong>Currency Protection:</strong> {step4_currencyProtected !== step3_greekConverted ? '✅ Made changes' : '❌ No changes'}</li>
          </ul>
          
          <h4>Specific Issues to Look For:</h4>
          <ul>
            <li><strong>Missing "T" in Theta:</strong> Check if LaTeX preprocessing is removing backslashes incorrectly</li>
            <li><strong>Omega showing as LaTeX:</strong> Check if Greek conversion is working but rendering is failing</li>
            <li><strong>Character encoding:</strong> Look for any character corruption</li>
          </ul>

          <h4>Character-by-Character Comparison:</h4>
          <div style={{ fontFamily: 'monospace', fontSize: '12px', backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            <div>Original "Theta": {JSON.stringify("Theta")}</div>
            <div>After LaTeX: {JSON.stringify(step2_latexPreprocessed.match(/Theta/g)?.[0] || 'NOT FOUND')}</div>
            <div>After Greek: {JSON.stringify(step3_greekConverted.match(/[Tt]heta|\$\\\\theta\$/g)?.[0] || 'NOT FOUND')}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugStepByStep;
