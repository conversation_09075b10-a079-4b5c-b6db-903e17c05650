import { useState, useMemo } from 'react';
import Card from '../../components/common/card/Card';
import profile from '../../assets/images/profile.png';
import { getTimeAgo } from '../../utils/timesAgo';
import useLocalStorage from '../../hooks/useLocalStorage';
import {
  useGetRecentProjectsPaginatedQuery,
  useDeleteProjectByIdMutation,
} from '../../services/projectsService';
import { useNavigate } from 'react-router-dom';
import ListCard from '../../components/common/card/ListCard';
import ConfirmDialog from '../../components/common/confirmDialog/ConfirmDialog';
import toast from 'react-hot-toast';
import { DataGridTemplate } from '../../components/templates/DataGridTemplate';
import { useGridData } from '../../hooks/useGridData';
import useUIPreferences from '../../hooks/useUIPreferences';

const MyProjectsPage = () => {
  const [currentUser] = useLocalStorage('user', null);
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [deleteProject] = useDeleteProjectByIdMutation();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // Use the UI preferences hook for persistent state
  const { viewMode, setViewMode, activeTab, setActiveTab } = useUIPreferences({
    pageKey: 'projects-page',
    defaultViewMode: 'grid',
    defaultActiveTab: 'templates',
  });

  const resetPage = () => setPage(1);

  // Fetch all projects
  const {
    data: projectsData,
    isLoading,
    error: isError,
    isFetching,
    refetch,
  } = useGetRecentProjectsPaginatedQuery(
    {
      userId: currentUser.id,
      page,
      size: 20,
    },
    {
      refetchOnMountOrArgChange: true,
    }
  );

  // Filter projects based on active tab
  const filteredProjectsData = useMemo(() => {
    if (!projectsData?.data?.content) return projectsData;

    const filteredContent = projectsData.data.content.filter((project: any) => {
      if (activeTab === 'templates') {
        return project.is_templated_project === true;
      } else {
        return project.is_templated_project !== true;
      }
    });

    return {
      ...projectsData,
      data: {
        ...projectsData.data,
        content: filteredContent,
      },
    };
  }, [projectsData, activeTab]);

  const currentData = filteredProjectsData;

  const hasNextPage = currentData?.data
    ? currentData.data.totalPages > currentData.data.currentPage
    : false;

  const {
    handleSearch,
    filteredData: filteredProjects,
    handleLoadMore,
    isFetchingNextPage,
    hasMore,
    resetAccumulatedData,
  } = useGridData({
    data: currentData?.data?.content,
    isLoading,
    isError,
    searchField: 'title',
    isPaginated: true,
    fetchNextPage: () => {
      if (!isFetching && hasNextPage) {
        setPage((prev) => prev + 1);
      }
    },
    isFetchingNextPage: isFetching && page > 1,
    hasNextPage,
    resetPage,
    sortField: 'timestamp',
    sortDirection: 'desc',
    currentPage: page,
    externalViewMode: viewMode,
    externalSetViewMode: setViewMode,
  });

  const handleViewClick = (recentProject: any) => {
    navigate('/chat', { state: { recentProject } });
  };

  const handleDelete = (projectId: string) => {
    setProjectToDelete(projectId);
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;
    try {
      await deleteProject(projectToDelete).unwrap();
      resetAccumulatedData();
      refetch(); // Fetch updated data without resetting pages
    } catch (error) {
      toast.error('Failed to delete project. Please try again.');
    } finally {
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
    }
  };

  const userArray = Array(5).fill({ name: 'Manzoor', avatar: profile });

  const renderCard = (project: any) => {
    const commonProps = {
      handleCardClick: () => handleViewClick(project),
      onDelete: () => handleDelete(project.id),
      role: 'creator' as const,
      initials: project?.title,
      status: project.status || 'Published',
      timeAgo: getTimeAgo(project.timestamp),
      title: project.title,
      fromProject: true,
      users: userArray,
    };

    return viewMode === 'grid' ? (
      <Card key={project.id} {...commonProps} />
    ) : (
      <ListCard key={project.id} {...commonProps} />
    );
  };

  // Custom header with tabs
  const ProjectTabs = () => (
    <div className="project-tabs">
      <button
        className={`project-tab ${activeTab === 'templates' ? 'active' : ''}`}
        onClick={() => {
          setActiveTab('templates');
          resetPage();
          resetAccumulatedData();
        }}
      >
        Templates
      </button>
      <button
        className={`project-tab ${activeTab === 'recent' ? 'active' : ''}`}
        onClick={() => {
          setActiveTab('recent');
          resetPage();
          resetAccumulatedData();
        }}
      >
        Recent Projects
      </button>
      <style>
        {`
        .project-tabs {
          display: flex;
          gap: 16px;
          margin-bottom: 20px;
          border-bottom: 1px solid var(--color-border);
        }
        .project-tab {
          padding: 12px 20px;
          background: transparent;
          border: none;
          border-bottom: 3px solid transparent;
          color: var(--color-text-secondary);
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
          position: relative;
          bottom: -1px;
        }
        .project-tab:hover {
          color: var(--color-primary);
        }
        .project-tab.active {
          color: var(--color-primary);
          border-bottom: 3px solid var(--color-primary);
          font-weight: 600;
        }
        `}
      </style>
    </div>
  );

  return (
    <>
      <DataGridTemplate
        isLoading={isLoading}
        isError={!!isError}
        isEmpty={!filteredProjects?.length}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        onSearch={handleSearch}
        loadingText={`Loading ${activeTab === 'templates' ? 'templates' : 'projects'}...`}
        errorTitle={`Error Loading ${activeTab === 'templates' ? 'Templates' : 'Projects'}`}
        errorMessage="There was a problem loading your data. Please try again."
        emptyTitle={`No ${activeTab === 'templates' ? 'Templates' : 'Projects'} Found`}
        emptyMessage={
          currentData?.data?.content?.length
            ? `No ${activeTab === 'templates' ? 'templates' : 'projects'} found matching your search.`
            : `You don't have any ${activeTab === 'templates' ? 'templates' : 'projects'} yet.`
        }
        hasMore={hasMore}
        onLoadMore={handleLoadMore}
        isLoadingMore={isFetchingNextPage}
        headerExtras={<ProjectTabs />}
      >
        {filteredProjects?.map(renderCard)}
      </DataGridTemplate>

      <ConfirmDialog
        isOpen={showDeleteConfirm}
        title="Delete Project"
        message="Are you sure you want to delete this project? This action cannot be undone."
        onConfirm={handleConfirmDelete}
        onCancel={() => {
          setShowDeleteConfirm(false);
          setProjectToDelete(null);
        }}
      />
    </>
  );
};

export default MyProjectsPage;
