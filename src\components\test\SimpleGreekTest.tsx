import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../common/chips/Markdown';

const SimpleGreekTest: React.FC = () => {
  // Test content that matches your backend response format
  const testContent = `# Table of Greek Symbols

Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |
| Delta | Delta | \\delta |
| Epsilon | Epsilon | \\epsilon |
| Lambda | Lambda | \\lambda |
| Mu | Mu | \\mu |
| Sigma | Sigma | \\sigma |
| Pi | Pi | \\pi |
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |

## Test Cases

1. **Simple Greek letters**: Alpha, Beta, Gamma should render as α, β, γ
2. **In sentences**: The angle Theta is measured in radians, and <PERSON> equals 3.14159
3. **Mixed with math**: The equation $E = mc^2$ uses various symbols
4. **Currency protection**: This costs $100 and that costs $200 (should remain as text)
5. **Direct LaTeX**: $\\alpha + \\beta = \\gamma$ (should render as math)`;

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '800px', 
      margin: '0 auto',
      backgroundColor: '#f9f9f9',
      borderRadius: '8px'
    }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Simple Greek Symbol Test</h1>
      
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #ddd'
      }}>
        <MarkdownRenderer content={testContent} />
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '16px', 
        backgroundColor: '#e8f5e8', 
        borderRadius: '8px',
        border: '1px solid #4caf50'
      }}>
        <h3 style={{ color: '#2e7d32', marginBottom: '10px' }}>✅ Expected Results:</h3>
        <ul style={{ color: '#2e7d32', fontSize: '14px' }}>
          <li>The "Symbol" column should show actual Greek letters: α, β, γ, δ, ε, λ, μ, σ, π, θ, φ, ω</li>
          <li>The "Name" column should show the converted symbols: α, β, γ, etc.</li>
          <li>The "LaTeX Code" column should show the LaTeX code as text: \alpha, \beta, etc.</li>
          <li>Greek letters in sentences should be converted to symbols</li>
          <li>Currency amounts ($100, $200) should remain as plain text</li>
          <li>Direct LaTeX ($\\alpha + \\beta = \\gamma$) should render as mathematical symbols</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '16px', 
        backgroundColor: '#fff3cd', 
        borderRadius: '8px',
        border: '1px solid #ffc107'
      }}>
        <h3 style={{ color: '#856404', marginBottom: '10px' }}>🔍 If Still Not Working:</h3>
        <ol style={{ color: '#856404', fontSize: '14px' }}>
          <li>Open browser DevTools (F12) and check the Console tab for errors</li>
          <li>Look for KaTeX-related errors or warnings</li>
          <li>Check if the Greek letter conversion is happening (inspect the HTML)</li>
          <li>Verify that KaTeX CSS is loaded (check Network tab)</li>
          <li>Try refreshing the page to clear any cached issues</li>
        </ol>
      </div>
    </div>
  );
};

export default SimpleGreekTest;
