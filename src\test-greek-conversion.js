// Simple test to verify Greek letter conversion is working
// This can be run in the browser console to test the function

import { convertGreekLetters, preprocessMarkdownContent } from './utils/markdownUtils.js';

// Test the Greek letter conversion function
console.log('Testing Greek letter conversion...');

// Test case 1: Simple Greek letter names
const test1 = "Alpha Beta Gamma Delta";
const result1 = convertGreekLetters(test1);
console.log('Test 1 Input:', test1);
console.log('Test 1 Output:', result1);
console.log('Expected: $\\alpha$ $\\beta$ $\\gamma$ $\\delta$');
console.log('---');

// Test case 2: Mixed content like the backend response
const test2 = `Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |`;

const result2 = convertGreekLetters(test2);
console.log('Test 2 Input:', test2);
console.log('Test 2 Output:', result2);
console.log('---');

// Test case 3: Full preprocessing pipeline
const test3 = "The Greek letters Alpha and Beta are important. Price is $100.";
const result3 = preprocessMarkdownContent(test3, {
  enableGreekConversion: true,
  enableCurrencyProtection: true,
  enableLatexPreprocessing: true
});
console.log('Test 3 Input:', test3);
console.log('Test 3 Output:', result3);
console.log('Expected: Greek letters converted, currency protected');
console.log('---');

// Test case 4: Backend response format
const backendResponse = `"summary": "Thank you for uploading your document. What would you like to do next ?",

Table of Greek Symbols
Here is a table of common Greek symbols used in mathematics and science, along with their names and LaTeX representations:

| Symbol | Name | LaTeX Code |
|--------|------|------------|
| Alpha | Alpha | \\alpha |
| Beta | Beta | \\beta |
| Gamma | Gamma | \\gamma |
| Delta | Delta | \\delta |
| Epsilon | Epsilon | \\epsilon |
| Lambda | Lambda | \\lambda |
| Mu | Mu | \\mu |
| Sigma | Sigma | \\sigma |
| Pi | Pi | \\pi |
| Theta | Theta | \\theta |
| Phi | Phi | \\phi |
| Omega | Omega | \\omega |`;

const result4 = preprocessMarkdownContent(backendResponse, {
  enableGreekConversion: true,
  enableCurrencyProtection: true,
  enableLatexPreprocessing: true
});
console.log('Test 4 - Backend Response Input:', backendResponse);
console.log('Test 4 - Backend Response Output:', result4);
console.log('---');

console.log('Greek letter conversion test completed!');
